# 记忆模块
# 使用mem0库保存和检索家庭成员信息

from mem0 import Mem0

class MemoryModule:
    def __init__(self):
        # 初始化mem0客户端
        self.mem0 = Mem0()
        
    def save_family_member(self, member_info):
        """
        保存家庭成员信息
        :param member_info: 包含姓名、关系、联系方式等信息的字典
        """
        # 将信息保存到mem0
        self.mem0.save(member_info)
        
    def get_family_member(self, name):
        """
        根据姓名获取家庭成员信息
        :param name: 家庭成员姓名
        :return: 包含该成员信息的字典
        """
        # 从mem0检索信息
        return self.mem0.get(name)
    
    def list_all_members(self):
        """
        列出所有保存的家庭成员
        :return: 家庭成员列表
        """
        # 获取所有保存的信息
        return self.mem0.list_all()
