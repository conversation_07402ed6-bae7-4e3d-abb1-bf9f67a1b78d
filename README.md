# 家庭助手语音代理系统

一个实时standby的智能家庭助手，具备语音识别、说话人识别、会话管理、LLM分析决策和工具调用能力。

## 🏗️ 架构概览

### 分层设计
```
┌─────────────────────────────────────────────────────────────┐
│                    控制平面 (FastAPI)                        │
│  家庭成员管理 | 声纹注册 | LLM配置 | 设备管理 | 会话历史      │
├─────────────────────────────────────────────────────────────┤
│                    数据平面 (Standby Agent)                  │
│  音频采集 → ASR → 说话人识别 → 会话管理 → LLM分析 → 工具执行  │
├─────────────────────────────────────────────────────────────┤
│                    存储层 (Local + Cloud)                    │
│  声纹库 | 家庭档案 | 会话历史 | 设备状态 | 配置信息           │
└─────────────────────────────────────────────────────────────┘
```

### 核心特性
- ✅ **本地语音识别**: 使用 faster-whisper，保护隐私
- ✅ **结构化LLM输出**: OpenAI API + Pydantic 模型
- ✅ **FastAPI Web管理**: RESTful API + 自动文档
- ✅ **类型安全**: 全面的 Pydantic 数据模型
- ✅ **测试驱动开发**: 41个单元测试，100%通过
- 🚧 **说话人识别**: whisperX + 声纹匹配（待实现）
- 🚧 **智能会话管理**: 基于时间和语义的切分（待实现）
- 🚧 **工具调用系统**: 可扩展的工具注册与执行（待实现）

## 🚀 快速开始

### 环境要求
- Python 3.13+
- pip 或 poetry

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行演示
```bash
# 查看系统演示
python demo.py

# 启动 FastAPI 服务器
python -m uvicorn src.api.main:app --reload

# 访问 API 文档
# http://localhost:8000/docs
```

### 运行测试
```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试
python -m pytest tests/test_models.py -v
python -m pytest tests/test_api.py -v
python -m pytest tests/test_llm_client.py -v
```

## 📁 项目结构

```
home-assistant/
├── src/
│   ├── shared/
│   │   └── models.py          # 核心 Pydantic 数据模型
│   ├── api/
│   │   ├── main.py           # FastAPI 应用入口
│   │   └── routers/          # API 路由模块
│   │       ├── family.py     # 家庭成员管理
│   │       ├── voiceprints.py # 声纹管理
│   │       ├── llm.py        # LLM 配置
│   │       ├── devices.py    # 设备管理
│   │       ├── conversations.py # 会话历史
│   │       └── system.py     # 系统控制
│   └── llm/
│       ├── client.py         # OpenAI 客户端
│       └── schemas.py        # LLM 相关模型
├── tests/
│   ├── test_models.py        # 数据模型测试
│   ├── test_api.py          # API 路由测试
│   └── test_llm_client.py   # LLM 客户端测试
├── docs/
│   └── architecture.md      # 详细架构文档
├── demo.py                  # 系统演示脚本
├── requirements.txt         # 依赖列表
└── README.md               # 项目说明
```

## 🔧 API 接口

### 家庭成员管理
- `GET /api/v1/family/members` - 列出所有成员
- `POST /api/v1/family/members` - 添加新成员
- `GET /api/v1/family/members/{id}` - 获取成员详情
- `PUT /api/v1/family/members/{id}` - 更新成员信息
- `DELETE /api/v1/family/members/{id}` - 删除成员

### 声纹管理
- `GET /api/v1/voiceprints/` - 列出所有声纹
- `POST /api/v1/voiceprints/enroll` - 录制新声纹
- `POST /api/v1/voiceprints/verify` - 验证声纹
- `DELETE /api/v1/voiceprints/{id}` - 删除声纹

### LLM 配置
- `GET /api/v1/llm/config` - 获取当前配置
- `PUT /api/v1/llm/config` - 更新配置
- `POST /api/v1/llm/test` - 测试连接
- `GET /api/v1/llm/models` - 列出可用模型

### 设备管理
- `GET /api/v1/devices/` - 列出所有设备
- `POST /api/v1/devices/register` - 注册新设备
- `POST /api/v1/devices/{id}/control` - 控制设备
- `POST /api/v1/devices/discover` - 发现设备

### 系统控制
- `GET /api/v1/system/status` - 获取系统状态
- `POST /api/v1/system/start` - 启动 standby 模式
- `POST /api/v1/system/stop` - 停止 standby 模式
- `GET /api/v1/system/logs` - 获取系统日志

## 🧪 测试覆盖

- **数据模型测试**: 14个测试，验证 Pydantic 模型的创建、验证和序列化
- **API 路由测试**: 17个测试，覆盖所有 REST 端点的正常和异常情况
- **LLM 客户端测试**: 10个测试，包括成功调用、错误处理和模拟响应

总计：**41个测试，100%通过**

## 🔮 下一步开发计划

### Phase 1: 语音处理管线
- [ ] 音频采集与 VAD (Voice Activity Detection)
- [ ] ASR 集成优化
- [ ] 说话人分离与识别
- [ ] 声纹注册与匹配

### Phase 2: 会话管理
- [ ] 基于时间间隔的会话切分
- [ ] 语义相关性分析
- [ ] 上下文摘要生成
- [ ] 会话状态管理

### Phase 3: 工具系统
- [ ] 工具注册框架
- [ ] 网络搜索工具
- [ ] 日历集成
- [ ] 家居设备控制
- [ ] MCP 客户端集成

### Phase 4: 数据持久化
- [ ] SQLite/PostgreSQL 集成
- [ ] 数据迁移脚本
- [ ] 缓存层 (Redis)
- [ ] 备份与恢复

### Phase 5: 生产就绪
- [ ] Docker 容器化
- [ ] 监控与日志
- [ ] 性能优化
- [ ] 安全加固

## 📖 详细文档

- [架构设计文档](docs/architecture.md) - 完整的系统架构说明
- [API 文档](http://localhost:8000/docs) - 启动服务器后访问交互式文档

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [FastAPI](https://fastapi.tiangolo.com/) - 现代、快速的 Web 框架
- [Pydantic](https://pydantic-docs.helpmanual.io/) - 数据验证和设置管理
- [OpenAI](https://openai.com/) - 强大的语言模型 API
- [faster-whisper](https://github.com/guillaumekln/faster-whisper) - 高效的语音识别
