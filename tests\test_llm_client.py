"""
测试 LLM 客户端
遵循 TDD 原则，先定义期望的 LLM 客户端行为
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
import json

from src.llm.client import LLMClient
from src.llm.schemas import ConversationAnalysisRequest, ConversationAnalysisResponse
from src.shared.models import LLMConfig, LLMAnalysisResult, ToolCall, HomeAction


class TestLLMClient:
    """测试 LLM 客户端"""
    
    @pytest.fixture
    def llm_config(self):
        """创建测试用的 LLM 配置"""
        return LLMConfig(
            provider="openai",
            api_key="sk-test123",
            model="gpt-4o-mini",
            temperature=0.2
        )
    
    @pytest.fixture
    def llm_client(self, llm_config):
        """创建 LLM 客户端实例"""
        return LLMClient(llm_config)
    
    def test_client_initialization(self, llm_config):
        """测试客户端初始化"""
        client = LLMClient(llm_config)
        assert client.config == llm_config
        assert client.client is not None
    
    def test_client_initialization_with_custom_base_url(self):
        """测试使用自定义 base_url 初始化客户端"""
        config = LLMConfig(
            provider="openai",
            api_key="sk-test123",
            model="gpt-4o-mini",
            base_url="https://api.custom.com/v1"
        )
        client = LLMClient(config)
        assert client.config.base_url == "https://api.custom.com/v1"
    
    def test_analyze_conversation_success(self, llm_client):
        """测试成功的对话分析"""
        # 模拟 OpenAI 响应
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "should_intervene": True,
            "reasons": "用户询问天气信息，需要调用天气查询工具",
            "suggested_tools": [
                {"name": "weather_search", "args": {"location": "北京"}}
            ],
            "home_actions": [],
            "reply_text": "我来为您查询北京的天气情况",
            "priority": "normal",
            "safety_flags": []
        })
        mock_response.usage.total_tokens = 150

        # Mock the client's chat.completions.create method
        llm_client.client.chat.completions.create = Mock(return_value=mock_response)
        
        # 创建分析请求
        request = ConversationAnalysisRequest(
            conversation_text="今天北京天气怎么样？",
            participants=["user_1"],
            context="用户询问天气"
        )
        
        # 执行分析
        result = llm_client.analyze_conversation(request)
        
        # 验证结果
        assert isinstance(result, ConversationAnalysisResponse)
        assert result.analysis.should_intervene is True
        assert len(result.analysis.suggested_tools) == 1
        assert result.analysis.suggested_tools[0].name == "weather_search"
        assert result.analysis.reply_text == "我来为您查询北京的天气情况"
    
    def test_analyze_conversation_no_intervention(self, llm_client):
        """测试不需要介入的对话分析"""
        # 模拟 OpenAI 响应
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "should_intervene": False,
            "reasons": "对话内容为日常闲聊，无需助手介入",
            "suggested_tools": [],
            "home_actions": [],
            "reply_text": None,
            "priority": "low",
            "safety_flags": []
        })
        mock_response.usage.total_tokens = 100

        # Mock the client's chat.completions.create method
        llm_client.client.chat.completions.create = Mock(return_value=mock_response)
        
        # 创建分析请求
        request = ConversationAnalysisRequest(
            conversation_text="今天心情不错",
            participants=["user_1"],
            context="日常对话"
        )
        
        # 执行分析
        result = llm_client.analyze_conversation(request)
        
        # 验证结果
        assert result.analysis.should_intervene is False
        assert len(result.analysis.suggested_tools) == 0
        assert result.analysis.reply_text is None
    
    def test_analyze_conversation_with_home_actions(self, llm_client):
        """测试包含家居控制的对话分析"""
        # 模拟 OpenAI 响应
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "should_intervene": True,
            "reasons": "用户要求开灯，需要控制智能家居设备",
            "suggested_tools": [],
            "home_actions": [
                {
                    "domain": "light",
                    "entity_id": "light.living_room",
                    "action": "turn_on",
                    "params": {"brightness": 80}
                }
            ],
            "reply_text": "好的，我来为您打开客厅的灯",
            "priority": "normal",
            "safety_flags": []
        })
        mock_response.usage.total_tokens = 120

        # Mock the client's chat.completions.create method
        llm_client.client.chat.completions.create = Mock(return_value=mock_response)
        
        # 创建分析请求
        request = ConversationAnalysisRequest(
            conversation_text="请打开客厅的灯",
            participants=["user_1"],
            context="家居控制请求"
        )
        
        # 执行分析
        result = llm_client.analyze_conversation(request)
        
        # 验证结果
        assert result.analysis.should_intervene is True
        assert len(result.analysis.home_actions) == 1
        assert result.analysis.home_actions[0].domain == "light"
        assert result.analysis.home_actions[0].action == "turn_on"
    
    def test_analyze_conversation_api_error(self, llm_client):
        """测试 API 调用错误处理"""
        # 模拟 API 错误
        llm_client.client.chat.completions.create = Mock(side_effect=Exception("API Error"))
        
        request = ConversationAnalysisRequest(
            conversation_text="测试错误处理",
            participants=["user_1"]
        )
        
        # 执行分析，应该抛出异常
        with pytest.raises(Exception):
            llm_client.analyze_conversation(request)
    
    def test_analyze_conversation_invalid_json(self, llm_client):
        """测试无效 JSON 响应处理"""
        # 模拟无效 JSON 响应
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "invalid json response"

        # Mock the client's chat.completions.create method
        llm_client.client.chat.completions.create = Mock(return_value=mock_response)
        
        request = ConversationAnalysisRequest(
            conversation_text="测试无效JSON",
            participants=["user_1"]
        )
        
        # 执行分析，应该抛出异常
        with pytest.raises(Exception):
            llm_client.analyze_conversation(request)
    
    def test_build_system_prompt(self, llm_client):
        """测试系统提示构建"""
        prompt = llm_client._build_system_prompt()

        assert "家庭助手" in prompt
        assert "JSON" in prompt
        assert "介入" in prompt  # 改为检查中文"介入"而不是英文字段名
    
    def test_build_user_prompt(self, llm_client):
        """测试用户提示构建"""
        request = ConversationAnalysisRequest(
            conversation_text="今天天气怎么样？",
            participants=["张三", "李四"],
            context="用户询问天气",
            available_tools=["weather_search", "calendar_check"]
        )
        
        prompt = llm_client._build_user_prompt(request)
        
        assert "今天天气怎么样？" in prompt
        assert "张三" in prompt
        assert "李四" in prompt
        assert "weather_search" in prompt
    
    def test_validate_response_structure(self, llm_client):
        """测试响应结构验证"""
        # 有效响应
        valid_response = {
            "should_intervene": True,
            "reasons": "测试原因",
            "suggested_tools": [],
            "home_actions": [],
            "reply_text": "测试回复",
            "priority": "normal",
            "safety_flags": []
        }
        
        # 应该不抛出异常
        result = LLMAnalysisResult(**valid_response)
        assert result.should_intervene is True
        
        # 无效响应（缺少必需字段）
        invalid_response = {
            "should_intervene": True
            # 缺少 reasons 字段
        }
        
        with pytest.raises(Exception):
            LLMAnalysisResult(**invalid_response)
