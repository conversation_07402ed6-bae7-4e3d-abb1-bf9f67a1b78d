"""
FastAPI 主应用
家庭助手语音代理的 Web API 服务
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime

from .routers import family, voiceprints, llm, devices, conversations, system
from ..shared.models import APIResponse


# 创建 FastAPI 应用
app = FastAPI(
    title="Home Assistant Voice Agent API",
    description="RESTful API for managing voice agent configuration and control",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含路由
app.include_router(family.router, prefix="/api/v1/family", tags=["family"])
app.include_router(voiceprints.router, prefix="/api/v1/voiceprints", tags=["voiceprints"])
app.include_router(llm.router, prefix="/api/v1/llm", tags=["llm"])
app.include_router(devices.router, prefix="/api/v1/devices", tags=["devices"])
app.include_router(conversations.router, prefix="/api/v1/conversations", tags=["conversations"])
app.include_router(system.router, prefix="/api/v1/system", tags=["system"])


@app.get("/health", response_model=APIResponse)
async def health_check():
    """健康检查端点"""
    return APIResponse(
        success=True,
        message="Service is healthy",
        data={"timestamp": datetime.now().isoformat()}
    )


@app.get("/", response_model=APIResponse)
async def root():
    """根端点"""
    return APIResponse(
        success=True,
        message="Home Assistant Voice Agent API",
        data={
            "version": "1.0.0",
            "docs": "/docs",
            "health": "/health"
        }
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
