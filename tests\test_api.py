"""
测试 FastAPI 应用和路由
遵循 TDD 原则，先定义期望的 API 行为
"""

import pytest
from fastapi.testclient import TestClient
from datetime import datetime

from src.api.main import app


@pytest.fixture
def client():
    """创建测试客户端"""
    return TestClient(app)


class TestHealthCheck:
    """测试健康检查端点"""
    
    def test_health_check(self, client):
        """测试健康检查"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Service is healthy"
        assert "timestamp" in data["data"]


class TestFamilyAPI:
    """测试家庭成员管理 API"""
    
    def test_list_family_members_empty(self, client):
        """测试列出空的家庭成员列表"""
        response = client.get("/api/v1/family/members")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"] == []
    
    def test_create_family_member(self, client):
        """测试创建家庭成员"""
        member_data = {
            "display_name": "张三",
            "relationship": "父亲",
            "device_permissions": ["living_room.light"],
            "preferences": {"language": "zh-CN"}
        }
        response = client.post("/api/v1/family/members", json=member_data)
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert data["data"]["display_name"] == "张三"
        assert "user_id" in data["data"]
    
    def test_create_family_member_invalid_name(self, client):
        """测试创建家庭成员时名称验证"""
        member_data = {"display_name": ""}
        response = client.post("/api/v1/family/members", json=member_data)
        assert response.status_code == 422
    
    def test_get_family_member(self, client):
        """测试获取单个家庭成员"""
        # 先创建一个成员
        member_data = {"display_name": "李四"}
        create_response = client.post("/api/v1/family/members", json=member_data)
        user_id = create_response.json()["data"]["user_id"]
        
        # 获取成员信息
        response = client.get(f"/api/v1/family/members/{user_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["display_name"] == "李四"
    
    def test_get_nonexistent_family_member(self, client):
        """测试获取不存在的家庭成员"""
        response = client.get("/api/v1/family/members/nonexistent")
        assert response.status_code == 404


class TestVoiceprintAPI:
    """测试声纹管理 API"""
    
    def test_list_voiceprints_empty(self, client):
        """测试列出空的声纹列表"""
        response = client.get("/api/v1/voiceprints/")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"] == []
    
    def test_enroll_voiceprint(self, client):
        """测试声纹录制"""
        # 先创建一个家庭成员
        member_data = {"display_name": "王五"}
        member_response = client.post("/api/v1/family/members", json=member_data)
        user_id = member_response.json()["data"]["user_id"]
        
        # 录制声纹
        voiceprint_data = {
            "user_id": user_id,
            "audio_samples": ["base64_audio_sample_1", "base64_audio_sample_2"],
            "quality_threshold": 0.8
        }
        response = client.post("/api/v1/voiceprints/enroll", json=voiceprint_data)
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert "id" in data["data"]


class TestLLMConfigAPI:
    """测试 LLM 配置 API"""
    
    def test_get_llm_config_default(self, client):
        """测试获取默认 LLM 配置"""
        response = client.get("/api/v1/llm/config")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["provider"] == "openai"
        assert data["data"]["model"] == "gpt-4o-mini"
    
    def test_update_llm_config(self, client):
        """测试更新 LLM 配置"""
        config_data = {
            "provider": "openai",
            "api_key": "sk-test123",
            "model": "gpt-4o",
            "temperature": 0.1
        }
        response = client.put("/api/v1/llm/config", json=config_data)
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["model"] == "gpt-4o"
    
    def test_test_llm_connection(self, client):
        """测试 LLM 连接测试"""
        response = client.post("/api/v1/llm/test")
        # 由于没有真实的 API key，预期会失败
        assert response.status_code in [200, 400]


class TestDeviceAPI:
    """测试设备管理 API"""
    
    def test_list_devices_empty(self, client):
        """测试列出空的设备列表"""
        response = client.get("/api/v1/devices/")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"] == []
    
    def test_register_device(self, client):
        """测试注册设备"""
        device_data = {
            "name": "客厅灯",
            "device_type": "light",
            "entity_id": "light.living_room",
            "capabilities": ["turn_on", "turn_off", "set_brightness"]
        }
        response = client.post("/api/v1/devices/register", json=device_data)
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert data["data"]["name"] == "客厅灯"


class TestSystemAPI:
    """测试系统控制 API"""
    
    def test_get_system_status(self, client):
        """测试获取系统状态"""
        response = client.get("/api/v1/system/status")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "standby_active" in data["data"]
        assert "devices_count" in data["data"]
    
    def test_start_standby(self, client):
        """测试启动 standby"""
        response = client.post("/api/v1/system/start")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_stop_standby(self, client):
        """测试停止 standby"""
        response = client.post("/api/v1/system/stop")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True


class TestConversationAPI:
    """测试会话历史 API"""
    
    def test_list_conversations_empty(self, client):
        """测试列出会话列表（可能包含示例数据）"""
        response = client.get("/api/v1/conversations/")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "conversations" in data["data"]
        assert "total" in data["data"]
