"""
家庭成员管理 API 路由
"""

from fastapi import APIRouter, HTTPException, status
from typing import List
import uuid
from datetime import datetime

from ...shared.models import (
    FamilyMember, 
    FamilyMemberCreate, 
    APIResponse
)

router = APIRouter()

# 临时内存存储（实际应用中应该使用数据库）
_family_members: dict[str, FamilyMember] = {}


@router.get("/members", response_model=APIResponse)
async def list_family_members():
    """列出所有家庭成员"""
    members = list(_family_members.values())
    return APIResponse(
        success=True,
        message=f"Found {len(members)} family members",
        data=members
    )


@router.post("/members", response_model=APIResponse, status_code=status.HTTP_201_CREATED)
async def create_family_member(member_data: FamilyMemberCreate):
    """创建新的家庭成员"""
    # 生成唯一 ID
    user_id = str(uuid.uuid4())
    now = datetime.now()
    
    # 创建家庭成员对象
    member = FamilyMember(
        user_id=user_id,
        display_name=member_data.display_name,
        relationship=member_data.relationship,
        contact_info=member_data.contact_info,
        device_permissions=member_data.device_permissions,
        preferences=member_data.preferences,
        created_at=now,
        updated_at=now,
        is_active=True
    )
    
    # 存储到内存
    _family_members[user_id] = member
    
    return APIResponse(
        success=True,
        message="Family member created successfully",
        data=member.model_dump()
    )


@router.get("/members/{user_id}", response_model=APIResponse)
async def get_family_member(user_id: str):
    """获取指定家庭成员信息"""
    if user_id not in _family_members:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Family member not found"
        )
    
    member = _family_members[user_id]
    return APIResponse(
        success=True,
        message="Family member found",
        data=member.model_dump()
    )


@router.put("/members/{user_id}", response_model=APIResponse)
async def update_family_member(user_id: str, member_data: FamilyMemberCreate):
    """更新家庭成员信息"""
    if user_id not in _family_members:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Family member not found"
        )
    
    # 获取现有成员
    existing_member = _family_members[user_id]
    
    # 更新信息
    updated_member = FamilyMember(
        user_id=user_id,
        display_name=member_data.display_name,
        relationship=member_data.relationship,
        contact_info=member_data.contact_info,
        device_permissions=member_data.device_permissions,
        preferences=member_data.preferences,
        created_at=existing_member.created_at,
        updated_at=datetime.now(),
        voiceprint_id=existing_member.voiceprint_id,
        is_active=existing_member.is_active
    )
    
    _family_members[user_id] = updated_member
    
    return APIResponse(
        success=True,
        message="Family member updated successfully",
        data=updated_member.model_dump()
    )


@router.delete("/members/{user_id}", response_model=APIResponse)
async def delete_family_member(user_id: str):
    """删除家庭成员"""
    if user_id not in _family_members:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Family member not found"
        )
    
    # 软删除：标记为非活跃
    member = _family_members[user_id]
    member.is_active = False
    member.updated_at = datetime.now()
    
    return APIResponse(
        success=True,
        message="Family member deleted successfully",
        data={"user_id": user_id}
    )
