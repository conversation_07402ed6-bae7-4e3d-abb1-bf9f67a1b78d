"""
核心 Pydantic 数据模型
定义系统中所有实体的数据结构
"""

from pydantic import BaseModel, Field, field_validator
from typing import List, Optional, Dict, Any, Literal
from datetime import datetime
from enum import Enum


class FamilyMemberCreate(BaseModel):
    """创建家庭成员的请求模型"""
    display_name: str = Field(..., min_length=1, max_length=50, description="显示名称")
    relationship: Optional[str] = Field(None, description="家庭关系")
    contact_info: Optional[Dict[str, str]] = Field(None, description="联系信息")
    device_permissions: List[str] = Field(default_factory=list, description="设备权限列表")
    preferences: Dict[str, Any] = Field(default_factory=dict, description="个人偏好设置")


class FamilyMember(FamilyMemberCreate):
    """家庭成员完整模型"""
    user_id: str = Field(..., description="用户唯一标识")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    voiceprint_id: Optional[str] = Field(None, description="关联的声纹ID")
    is_active: bool = Field(True, description="是否激活")


class VoiceprintCreate(BaseModel):
    """创建声纹的请求模型"""
    user_id: str = Field(..., description="关联的用户ID")
    audio_samples: List[str] = Field(..., min_length=1, description="Base64编码的音频样本")
    quality_threshold: float = Field(0.8, ge=0.0, le=1.0, description="质量阈值")


class Voiceprint(BaseModel):
    """声纹存储模型"""
    id: str = Field(..., description="声纹唯一标识")
    user_id: str = Field(..., description="关联的用户ID")
    embedding_data: str = Field(..., description="加密的声纹特征数据")
    quality_score: float = Field(..., ge=0.0, le=1.0, description="质量评分")
    created_at: datetime = Field(..., description="创建时间")
    sample_count: int = Field(..., ge=1, description="样本数量")


class LLMConfig(BaseModel):
    """LLM服务配置模型"""
    provider: str = Field("openai", description="服务提供商")
    api_key: str = Field(..., description="API密钥")
    model: str = Field("gpt-4o-mini", description="模型名称")
    base_url: Optional[str] = Field(None, description="自定义API地址")
    temperature: float = Field(0.2, ge=0.0, le=2.0, description="温度参数")
    max_tokens: Optional[int] = Field(None, gt=0, description="最大token数")
    timeout: int = Field(30, gt=0, description="超时时间(秒)")


class DeviceType(str, Enum):
    """设备类型枚举"""
    LIGHT = "light"
    CLIMATE = "climate"
    MEDIA_PLAYER = "media_player"
    SENSOR = "sensor"
    SWITCH = "switch"
    OTHER = "other"


class Device(BaseModel):
    """智能设备模型"""
    id: str = Field(..., description="设备唯一标识")
    name: str = Field(..., description="设备名称")
    device_type: DeviceType = Field(..., description="设备类型")
    entity_id: str = Field(..., description="实体ID")
    capabilities: List[str] = Field(..., description="设备能力列表")
    current_state: Dict[str, Any] = Field(..., description="当前状态")
    is_online: bool = Field(..., description="是否在线")
    last_seen: datetime = Field(..., description="最后见到时间")


class Utterance(BaseModel):
    """语句模型"""
    id: str = Field(..., description="语句唯一标识")
    session_id: Optional[str] = Field(None, description="所属会话ID")
    text: str = Field(..., description="识别的文本内容")
    start_ts: float = Field(..., description="开始时间戳")
    end_ts: float = Field(..., description="结束时间戳")
    speaker_temp_id: Optional[str] = Field(None, description="临时说话人ID")
    speaker_user_id: Optional[str] = Field(None, description="识别的用户ID")
    confidence: Optional[float] = Field(None, ge=0.0, le=1.0, description="ASR置信度")
    language: Optional[str] = Field(None, description="语言代码")

    @field_validator('end_ts')
    @classmethod
    def end_must_be_after_start(cls, v, info):
        if info.data.get('start_ts') and v <= info.data['start_ts']:
            raise ValueError('end_ts must be greater than start_ts')
        return v


class ConversationSession(BaseModel):
    """会话模型"""
    id: str = Field(..., description="会话唯一标识")
    started_at: Optional[float] = Field(None, description="开始时间戳")
    last_updated_at: Optional[float] = Field(None, description="最后更新时间戳")
    participants: List[str] = Field(default_factory=list, description="参与者用户ID列表")
    utterances: List[str] = Field(default_factory=list, description="语句ID列表")
    summary: Optional[str] = Field(None, description="会话摘要")
    status: Literal["active", "closed"] = Field("active", description="会话状态")


class ToolCall(BaseModel):
    """工具调用模型"""
    name: str = Field(..., description="工具名称")
    args: Dict[str, Any] = Field(default_factory=dict, description="工具参数")


class HomeAction(BaseModel):
    """家居控制动作模型"""
    domain: Literal["light", "climate", "media_player", "scene", "other"] = Field(..., description="设备域")
    entity_id: str = Field(..., description="实体ID")
    action: str = Field(..., description="动作名称")
    params: Dict[str, Any] = Field(default_factory=dict, description="动作参数")


class LLMAnalysisResult(BaseModel):
    """LLM分析结果模型"""
    should_intervene: bool = Field(..., description="是否应该介入")
    reasons: str = Field(..., description="分析原因")
    suggested_tools: List[ToolCall] = Field(default_factory=list, description="建议的工具调用")
    home_actions: List[HomeAction] = Field(default_factory=list, description="家居控制动作")
    reply_text: Optional[str] = Field(None, description="回复文本")
    priority: Literal["low", "normal", "high", "urgent"] = Field("normal", description="优先级")
    safety_flags: List[str] = Field(default_factory=list, description="安全标记")


class SystemStatus(BaseModel):
    """系统状态模型"""
    standby_active: bool = Field(..., description="standby是否激活")
    audio_capture_active: bool = Field(..., description="音频采集是否激活")
    llm_connected: bool = Field(..., description="LLM是否连接")
    devices_count: int = Field(..., ge=0, description="设备数量")
    active_sessions: int = Field(..., ge=0, description="活跃会话数")
    uptime_seconds: int = Field(..., ge=0, description="运行时间(秒)")
    last_activity: Optional[datetime] = Field(None, description="最后活动时间")


# API响应包装模型
class APIResponse(BaseModel):
    """通用API响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")


class PaginatedResponse(BaseModel):
    """分页响应模型"""
    items: List[Any] = Field(..., description="数据项列表")
    total: int = Field(..., ge=0, description="总数量")
    page: int = Field(..., ge=1, description="当前页码")
    size: int = Field(..., ge=1, description="页面大小")
    pages: int = Field(..., ge=1, description="总页数")
