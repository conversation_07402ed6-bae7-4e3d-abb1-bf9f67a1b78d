# 语音识别模块
# 集成faster-whisper和whisperX实现多说话人识别

import os
import wave
import scipy.io.wavfile as sf  # Added for WAV file writing
from faster_whisper import WhisperModel
from whisperX import load_model

class VoiceRecognition:
    def __init__(self, model_size="small"):
        # 加载语音识别模型
        self.model = WhisperModel(model_size)
        self.multi_speaker_model = load_model("medium")
        
    def transcribe(self, audio_file=None, duration=5):
        """
        将音频文件或实时音频转换为文本
        :param audio_file: 音频文件路径 (可选)
        :param duration: 实时录音时长 (秒)
        :return: 识别结果文本
        """
        import sounddevice as sd
        import numpy as np
        
        if audio_file:
            # 保留原有文件处理逻辑
            if not os.path.exists(audio_file):
                raise FileNotFoundError(f"音频文件 {audio_file} 不存在")
                
            if not self._is_wav_file(audio_file):
                raise ValueError("仅支持WAV格式音频文件")
                
            # 使用faster-whisper进行基础识别
            result = self.model.transcribe(audio_file)
                
            # 使用whisperX进行多说话人识别
            multi_speaker_result = self.multi_speaker_model.transcribe(audio_file)
                
            return {
                "basic_transcription": result["text"],
                "multi_speaker_analysis": multi_speaker_result["segments"]
            }
        else:
            try:
                # 实时录音逻辑
                print("开始录音，请说话...")
                audio = sd.rec(int(duration * 44100), samplerate=44100, channels=1)
                sd.wait()
                
                # 保存临时文件
                temp_file = "temp_recording.wav"
                sf.write(temp_file, audio, 44100, "PCM_16")
                
                # 使用faster-whisper进行基础识别
                result = self.model.transcribe(temp_file)
                
                # 使用whisperX进行多说话人识别
                multi_speaker_result = self.multi_speaker_model.transcribe(temp_file)
                
                # 清理临时文件
                os.remove(temp_file)
                
                return {
                    "basic_transcription": result["text"],
                    "multi_speaker_analysis": multi_speaker_result["segments"]
                }
            except Exception as e:
                print(f"录音或处理过程中发生错误: {str(e)}")
                return {
                    "basic_transcription": "录音失败",
                    "multi_speaker_analysis": []
                }
    
    def _is_wav_file(self, file_path):
        """检查文件是否为WAV格式"""
        with open(file_path, 'rb') as f:
            # WAV文件头标识
            header = f.read(4)
            return header == b'RIFF'
