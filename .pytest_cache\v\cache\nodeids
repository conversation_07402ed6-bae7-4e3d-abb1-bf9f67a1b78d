["tests/test_api.py::TestConversationAPI::test_list_conversations_empty", "tests/test_api.py::TestDeviceAPI::test_list_devices_empty", "tests/test_api.py::TestDeviceAPI::test_register_device", "tests/test_api.py::TestFamilyAPI::test_create_family_member", "tests/test_api.py::TestFamilyAPI::test_create_family_member_invalid_name", "tests/test_api.py::TestFamilyAPI::test_get_family_member", "tests/test_api.py::TestFamilyAPI::test_get_nonexistent_family_member", "tests/test_api.py::TestFamilyAPI::test_list_family_members_empty", "tests/test_api.py::TestHealthCheck::test_health_check", "tests/test_api.py::TestLLMConfigAPI::test_get_llm_config_default", "tests/test_api.py::TestLLMConfigAPI::test_test_llm_connection", "tests/test_api.py::TestLLMConfigAPI::test_update_llm_config", "tests/test_api.py::TestSystemAPI::test_get_system_status", "tests/test_api.py::TestSystemAPI::test_start_standby", "tests/test_api.py::TestSystemAPI::test_stop_standby", "tests/test_api.py::TestVoiceprintAPI::test_enroll_voiceprint", "tests/test_api.py::TestVoiceprintAPI::test_list_voiceprints_empty", "tests/test_llm_client.py::TestLLMClient::test_analyze_conversation_api_error", "tests/test_llm_client.py::TestLLMClient::test_analyze_conversation_invalid_json", "tests/test_llm_client.py::TestLLMClient::test_analyze_conversation_no_intervention", "tests/test_llm_client.py::TestLLMClient::test_analyze_conversation_success", "tests/test_llm_client.py::TestLLMClient::test_analyze_conversation_with_home_actions", "tests/test_llm_client.py::TestLLMClient::test_build_system_prompt", "tests/test_llm_client.py::TestLLMClient::test_build_user_prompt", "tests/test_llm_client.py::TestLLMClient::test_client_initialization", "tests/test_llm_client.py::TestLLMClient::test_client_initialization_with_custom_base_url", "tests/test_llm_client.py::TestLLMClient::test_validate_response_structure", "tests/test_models.py::TestConversation::test_conversation_session", "tests/test_models.py::TestConversation::test_utterance_creation", "tests/test_models.py::TestDevice::test_device_creation", "tests/test_models.py::TestFamilyMember::test_create_family_member_full", "tests/test_models.py::TestFamilyMember::test_create_family_member_minimal", "tests/test_models.py::TestFamilyMember::test_family_member_name_validation", "tests/test_models.py::TestFamilyMember::test_family_member_with_timestamps", "tests/test_models.py::TestLLMAnalysis::test_full_analysis_result", "tests/test_models.py::TestLLMAnalysis::test_minimal_analysis_result", "tests/test_models.py::TestLLMConfig::test_custom_llm_config", "tests/test_models.py::TestLLMConfig::test_default_llm_config", "tests/test_models.py::TestSystemStatus::test_system_status", "tests/test_models.py::TestVoiceprint::test_create_voiceprint_request", "tests/test_models.py::TestVoiceprint::test_voiceprint_model"]