# 家庭助手语音代理架构设计文档

## 项目概述

创建一个实时standby的智能家庭助手，具备语音识别、说话人识别、会话管理、LLM分析决策和工具调用能力。

### 核心特性
- 本地语音识别与说话人分离
- 基于对话间隔的智能会话切分
- OpenAI LLM远程分析与结构化输出
- 可扩展的工具调用系统
- FastAPI Web管理界面
- 隐私优先的数据处理

## 总体架构

### 分层设计
```
┌─────────────────────────────────────────────────────────────┐
│                    控制平面 (FastAPI)                        │
│  家庭成员管理 | 声纹注册 | LLM配置 | 设备管理 | 会话历史      │
├─────────────────────────────────────────────────────────────┤
│                    数据平面 (Standby Agent)                  │
│  音频采集 → ASR → 说话人识别 → 会话管理 → LLM分析 → 工具执行  │
├─────────────────────────────────────────────────────────────┤
│                    存储层 (Local + Cloud)                    │
│  声纹库 | 家庭档案 | 会话历史 | 设备状态 | 配置信息           │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. 音频处理层 (本地)
- **音频采集**: sounddevice + 环形缓冲
- **VAD**: WebRTC VAD / Silero VAD
- **ASR**: faster-whisper (本地推理)
- **说话人分离**: whisperX / pyannote
- **说话人识别**: 声纹匹配 (Resemblyzer/pyannote embeddings)

#### 2. 会话管理层 (本地)
- **SessionManager**: 基于时间间隔和语义相关性的会话切分
- **ConversationStore**: 会话持久化与上下文管理
- **参与者跟踪**: 多说话人会话状态维护

#### 3. LLM分析层 (远程)
- **OpenAI客户端**: 结构化输出 (Pydantic schema)
- **决策分析**: 是否介入、工具选择、设备控制
- **安全过滤**: 内容安全与权限校验

#### 4. 工具执行层 (本地+远程)
- **工具注册表**: 动态工具发现与注册
- **执行引擎**: 并发执行、超时控制、错误处理
- **内置工具**: 网络搜索、MCP客户端、日历、导航、家居控制

#### 5. FastAPI控制层 (本地Web服务)
- **家庭成员管理**: CRUD操作、权限配置
- **声纹管理**: 录制、验证、删除
- **LLM配置**: 服务商、模型、参数设置
- **设备管理**: 发现、注册、控制
- **系统监控**: 状态查询、日志查看、实时事件

## 数据模型 (Pydantic)

### 核心实体
```python
class FamilyMember(BaseModel):
    user_id: str
    display_name: str
    relationship: Optional[str]
    voiceprint_id: Optional[str]
    device_permissions: List[str]
    preferences: Dict[str, Any]

class Utterance(BaseModel):
    id: str
    session_id: str
    text: str
    start_ts: float
    end_ts: float
    speaker_user_id: Optional[str]
    confidence: float

class ConversationSession(BaseModel):
    id: str
    participants: List[str]
    utterances: List[str]
    summary: Optional[str]
    status: Literal["active", "closed"]

class LLMAnalysisResult(BaseModel):
    should_intervene: bool
    reasons: str
    suggested_tools: List[ToolCall]
    home_actions: List[HomeAction]
    reply_text: Optional[str]
    priority: Literal["low", "normal", "high", "urgent"]
```

## API设计

### RESTful端点
- `GET/POST /api/v1/family/members` - 家庭成员管理
- `POST /api/v1/voiceprints/enroll` - 声纹录制
- `PUT /api/v1/llm/config` - LLM服务配置
- `GET/POST /api/v1/devices/` - 智能设备管理
- `GET /api/v1/conversations/` - 会话历史查询
- `WebSocket /api/v1/system/events` - 实时状态推送

## 技术栈

### 本地组件
- **语音**: faster-whisper, whisperX, sounddevice
- **Web框架**: FastAPI, WebSocket
- **数据**: SQLite/PostgreSQL, Pydantic
- **AI**: pyannote, Resemblyzer

### 远程服务
- **LLM**: OpenAI GPT-4o-mini
- **工具**: 搜索API, MCP服务, 地图API

## 安全与隐私

- 音频数据仅本地处理，不上传云端
- 声纹数据加密存储
- API访问控制与权限管理
- 敏感操作需要用户确认
- 可配置的数据保留策略

## 部署架构

### 开发环境
- 本地运行所有组件
- SQLite数据库
- 文件系统存储

### 生产环境
- Docker容器化部署
- PostgreSQL数据库
- Redis缓存
- 反向代理 (nginx)
- 可选HTTPS证书

## 实施计划

### Phase 1: 核心数据模型与API框架
- Pydantic模型定义
- FastAPI应用骨架
- 基础CRUD操作
- 单元测试覆盖

### Phase 2: 语音处理管线
- 音频采集与VAD
- ASR集成
- 说话人分离与识别
- 会话管理逻辑

### Phase 3: LLM集成与工具系统
- OpenAI客户端与结构化输出
- 工具注册与执行框架
- 基础工具实现

### Phase 4: 端到端集成
- 完整数据流打通
- Web界面开发
- 系统监控与日志
- 性能优化

### Phase 5: 生产就绪
- 安全加固
- 部署自动化
- 监控告警
- 文档完善
