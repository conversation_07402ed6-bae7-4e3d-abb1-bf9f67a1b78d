"""
智能设备管理 API 路由
"""

from fastapi import APIRouter, HTTPException, status
from typing import List, Dict, Any
import uuid
from datetime import datetime

from ...shared.models import Device, DeviceType, APIResponse

router = APIRouter()

# 临时内存存储
_devices: dict[str, Device] = {}


@router.get("/", response_model=APIResponse)
async def list_devices():
    """列出所有设备"""
    devices = list(_devices.values())
    return APIResponse(
        success=True,
        message=f"Found {len(devices)} devices",
        data=devices
    )


@router.post("/register", response_model=APIResponse, status_code=status.HTTP_201_CREATED)
async def register_device(device_data: dict):
    """注册新设备"""
    # 生成唯一 ID
    device_id = str(uuid.uuid4())
    
    # 创建设备对象
    device = Device(
        id=device_id,
        name=device_data["name"],
        device_type=DeviceType(device_data["device_type"]),
        entity_id=device_data["entity_id"],
        capabilities=device_data.get("capabilities", []),
        current_state=device_data.get("current_state", {}),
        is_online=True,
        last_seen=datetime.now()
    )
    
    # 存储到内存
    _devices[device_id] = device
    
    return APIResponse(
        success=True,
        message="Device registered successfully",
        data=device.model_dump()
    )


@router.get("/{device_id}", response_model=APIResponse)
async def get_device(device_id: str):
    """获取指定设备信息"""
    if device_id not in _devices:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Device not found"
        )
    
    device = _devices[device_id]
    return APIResponse(
        success=True,
        message="Device found",
        data=device.model_dump()
    )


@router.post("/{device_id}/control", response_model=APIResponse)
async def control_device(device_id: str, control_data: dict):
    """控制设备"""
    if device_id not in _devices:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Device not found"
        )
    
    device = _devices[device_id]
    action = control_data.get("action")
    params = control_data.get("params", {})
    
    if not action:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Missing action parameter"
        )
    
    # 检查设备是否支持该动作
    if action not in device.capabilities:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Device does not support action: {action}"
        )
    
    # 模拟设备控制
    # 更新设备状态
    if action == "turn_on":
        device.current_state["state"] = "on"
    elif action == "turn_off":
        device.current_state["state"] = "off"
    elif action == "set_brightness" and "brightness" in params:
        device.current_state["brightness"] = params["brightness"]
    elif action == "set_temperature" and "temperature" in params:
        device.current_state["temperature"] = params["temperature"]
    
    # 更新其他参数
    device.current_state.update(params)
    device.last_seen = datetime.now()
    
    return APIResponse(
        success=True,
        message=f"Device {action} executed successfully",
        data={
            "device_id": device_id,
            "action": action,
            "params": params,
            "new_state": device.current_state
        }
    )


@router.post("/discover", response_model=APIResponse)
async def discover_devices():
    """发现新设备"""
    # 模拟设备发现
    discovered_devices = [
        {
            "name": "客厅灯",
            "device_type": "light",
            "entity_id": "light.living_room",
            "capabilities": ["turn_on", "turn_off", "set_brightness"]
        },
        {
            "name": "空调",
            "device_type": "climate",
            "entity_id": "climate.living_room",
            "capabilities": ["turn_on", "turn_off", "set_temperature", "set_mode"]
        }
    ]
    
    return APIResponse(
        success=True,
        message=f"Discovered {len(discovered_devices)} devices",
        data=discovered_devices
    )


@router.delete("/{device_id}", response_model=APIResponse)
async def remove_device(device_id: str):
    """移除设备"""
    if device_id not in _devices:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Device not found"
        )
    
    del _devices[device_id]
    
    return APIResponse(
        success=True,
        message="Device removed successfully",
        data={"device_id": device_id}
    )
