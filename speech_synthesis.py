# 语音合成模块
# 将文本转换为语音输出

import pyttsx3
from gtts import gTTS

class SpeechSynthesis:
    def __init__(self):
        # 初始化语音合成引擎
        self.engine = pyttsx3.init()
        
    def text_to_speech(self, text, output_file="output.mp3"):
        """
        将文本转换为语音并保存为文件
        :param text: 要转换的文本
        :param output_file: 输出文件路径
        """
        # 使用pyttsx3合成语音
        self.engine.save_to_file(text, output_file)
        self.engine.runAndWait()
        
        # 使用gTTS合成语音
        tts = gTTS(text=text, lang='zh-cn')
        tts.save(output_file)
