"""
LLM 配置管理 API 路由
"""

from fastapi import APIRouter, HTTPException, status
from typing import Optional

from ...shared.models import LLMConfig, APIResponse

router = APIRouter()

# 全局 LLM 配置（实际应用中应该持久化存储）
_llm_config: Optional[LLMConfig] = None


@router.get("/config", response_model=APIResponse)
async def get_llm_config():
    """获取当前 LLM 配置"""
    if _llm_config is None:
        # 返回默认配置
        default_config = LLMConfig(api_key="not_configured")
        return APIResponse(
            success=True,
            message="Default LLM configuration",
            data=default_config.model_dump(exclude={"api_key"})  # 不返回敏感信息
        )
    
    return APIResponse(
        success=True,
        message="Current LLM configuration",
        data=_llm_config.model_dump(exclude={"api_key"})
    )


@router.put("/config", response_model=APIResponse)
async def update_llm_config(config: LLMConfig):
    """更新 LLM 配置"""
    global _llm_config
    _llm_config = config
    
    return APIResponse(
        success=True,
        message="LLM configuration updated successfully",
        data=config.model_dump(exclude={"api_key"})
    )


@router.post("/test", response_model=APIResponse)
async def test_llm_connection():
    """测试 LLM 连接"""
    if _llm_config is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="LLM not configured"
        )
    
    # 模拟连接测试
    try:
        # 这里应该实际调用 LLM API 进行测试
        # 现在只是模拟
        if _llm_config.api_key == "not_configured" or not _llm_config.api_key.startswith("sk-"):
            raise Exception("Invalid API key")
        
        return APIResponse(
            success=True,
            message="LLM connection test successful",
            data={
                "provider": _llm_config.provider,
                "model": _llm_config.model,
                "status": "connected"
            }
        )
    
    except Exception as e:
        return APIResponse(
            success=False,
            message=f"LLM connection test failed: {str(e)}",
            data={
                "provider": _llm_config.provider,
                "model": _llm_config.model,
                "status": "failed"
            }
        )


@router.get("/models", response_model=APIResponse)
async def list_available_models():
    """列出可用的 LLM 模型"""
    # 模拟可用模型列表
    models = {
        "openai": [
            "gpt-4o",
            "gpt-4o-mini",
            "gpt-4-turbo",
            "gpt-3.5-turbo"
        ],
        "anthropic": [
            "claude-3-opus",
            "claude-3-sonnet",
            "claude-3-haiku"
        ]
    }
    
    return APIResponse(
        success=True,
        message="Available models retrieved",
        data=models
    )
