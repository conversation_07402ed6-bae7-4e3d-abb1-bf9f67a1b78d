# 主程序模块
# 协调各模块工作流程

from voice_recognition import VoiceRecognition
from memory_module import MemoryModule
from ai_processing import AIProcessing
from speech_synthesis import SpeechSynthesis

class HomeAssistant:
    def __init__(self):
        # 初始化各模块
        self.voice_recognition = VoiceRecognition()
        self.memory_module = MemoryModule()
        self.ai_processing = AIProcessing()
        self.speech_synthesis = SpeechSynthesis()
        
    def run(self):
        """启动家庭助手应用"""
        # 模拟语音输入
        audio_file = "input.wav"
        
        # 语音识别
        print("正在识别语音...")
        recognition_result = self.voice_recognition.transcribe(audio_file)
        print("语音识别结果:", recognition_result)
        
        # AI处理
        print("正在分析对话...")
        analysis_result = self.ai_processing.analyze_conversation(recognition_result["basic_transcription"])
        print("分析结果:", analysis_result)
        
        # 决策处理
        print("正在生成响应...")
        response = self.ai_processing.make_decision(analysis_result)
        print("响应内容:", response)
        
        # 语音合成
        print("正在生成语音输出...")
        self.speech_synthesis.text_to_speech(response)
        print("语音输出完成")
        
        # 保存家庭成员信息（示例）
        self.memory_module.save_family_member({
            "name": "张三",
            "relationship": "父亲",
            "contact": "13800000000"
        })
        
        # 获取家庭成员信息
        member_info = self.memory_module.get_family_member("张三")
        print("家庭成员信息:", member_info)

if __name__ == "__main__":
    assistant = HomeAssistant()
    assistant.run()
