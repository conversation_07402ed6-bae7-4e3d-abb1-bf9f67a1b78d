"""
系统控制和监控 API 路由
"""

from fastapi import APIRouter, HTTPException, status
from datetime import datetime
import time

from ...shared.models import SystemStatus, APIResponse

router = APIRouter()

# 全局系统状态
_system_state = {
    "standby_active": False,
    "audio_capture_active": False,
    "llm_connected": False,
    "start_time": time.time(),
    "last_activity": None
}


@router.get("/status", response_model=APIResponse)
async def get_system_status():
    """获取系统状态"""
    # 计算运行时间
    uptime_seconds = int(time.time() - _system_state["start_time"])
    
    # 模拟设备数量（实际应该从设备管理模块获取）
    from .devices import _devices
    devices_count = len(_devices)
    
    # 模拟活跃会话数量
    from .conversations import _conversations
    active_sessions = len([c for c in _conversations.values() if c.status == "active"])
    
    status = SystemStatus(
        standby_active=_system_state["standby_active"],
        audio_capture_active=_system_state["audio_capture_active"],
        llm_connected=_system_state["llm_connected"],
        devices_count=devices_count,
        active_sessions=active_sessions,
        uptime_seconds=uptime_seconds,
        last_activity=_system_state["last_activity"]
    )
    
    return APIResponse(
        success=True,
        message="System status retrieved",
        data=status.model_dump()
    )


@router.post("/start", response_model=APIResponse)
async def start_standby():
    """启动 standby 模式"""
    if _system_state["standby_active"]:
        return APIResponse(
            success=True,
            message="Standby is already active",
            data={"status": "already_active"}
        )
    
    # 启动 standby 模式
    _system_state["standby_active"] = True
    _system_state["audio_capture_active"] = True
    _system_state["last_activity"] = datetime.now()
    
    return APIResponse(
        success=True,
        message="Standby mode started successfully",
        data={
            "status": "started",
            "started_at": _system_state["last_activity"].isoformat()
        }
    )


@router.post("/stop", response_model=APIResponse)
async def stop_standby():
    """停止 standby 模式"""
    if not _system_state["standby_active"]:
        return APIResponse(
            success=True,
            message="Standby is already inactive",
            data={"status": "already_inactive"}
        )
    
    # 停止 standby 模式
    _system_state["standby_active"] = False
    _system_state["audio_capture_active"] = False
    _system_state["last_activity"] = datetime.now()
    
    return APIResponse(
        success=True,
        message="Standby mode stopped successfully",
        data={
            "status": "stopped",
            "stopped_at": _system_state["last_activity"].isoformat()
        }
    )


@router.get("/logs", response_model=APIResponse)
async def get_system_logs(
    level: str = "info",
    limit: int = 100,
    offset: int = 0
):
    """获取系统日志"""
    # 模拟日志数据
    logs = [
        {
            "timestamp": datetime.now().isoformat(),
            "level": "info",
            "module": "system",
            "message": "System status requested"
        },
        {
            "timestamp": (datetime.now()).isoformat(),
            "level": "info",
            "module": "audio",
            "message": "Audio capture initialized"
        },
        {
            "timestamp": (datetime.now()).isoformat(),
            "level": "warning",
            "module": "llm",
            "message": "LLM connection timeout, retrying..."
        }
    ]
    
    # 级别过滤
    if level != "all":
        logs = [log for log in logs if log["level"] == level]
    
    # 分页
    total = len(logs)
    logs = logs[offset:offset + limit]
    
    return APIResponse(
        success=True,
        message=f"Retrieved {len(logs)} log entries",
        data={
            "logs": logs,
            "total": total,
            "level": level,
            "limit": limit,
            "offset": offset
        }
    )


@router.post("/restart", response_model=APIResponse)
async def restart_system():
    """重启系统"""
    # 模拟重启过程
    _system_state["standby_active"] = False
    _system_state["audio_capture_active"] = False
    _system_state["llm_connected"] = False
    _system_state["start_time"] = time.time()
    _system_state["last_activity"] = datetime.now()
    
    return APIResponse(
        success=True,
        message="System restart initiated",
        data={
            "status": "restarting",
            "restart_time": _system_state["last_activity"].isoformat()
        }
    )


@router.get("/health", response_model=APIResponse)
async def detailed_health_check():
    """详细健康检查"""
    health_data = {
        "overall_status": "healthy",
        "components": {
            "audio_system": "healthy" if _system_state["audio_capture_active"] else "inactive",
            "llm_connection": "healthy" if _system_state["llm_connected"] else "disconnected",
            "standby_service": "active" if _system_state["standby_active"] else "inactive",
            "api_service": "healthy"
        },
        "uptime_seconds": int(time.time() - _system_state["start_time"]),
        "last_check": datetime.now().isoformat()
    }
    
    # 判断整体健康状态
    unhealthy_components = [
        name for name, status in health_data["components"].items() 
        if status not in ["healthy", "active"]
    ]
    
    if unhealthy_components:
        health_data["overall_status"] = "degraded"
        health_data["issues"] = unhealthy_components
    
    return APIResponse(
        success=True,
        message="Health check completed",
        data=health_data
    )
