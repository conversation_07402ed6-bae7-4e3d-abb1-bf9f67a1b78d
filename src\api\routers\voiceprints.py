"""
声纹管理 API 路由
"""

from fastapi import APIRouter, HTTPException, status
from typing import List
import uuid
from datetime import datetime

from ...shared.models import (
    Voiceprint, 
    VoiceprintCreate, 
    APIResponse
)

router = APIRouter()

# 临时内存存储
_voiceprints: dict[str, Voiceprint] = {}


@router.get("/", response_model=APIResponse)
async def list_voiceprints():
    """列出所有声纹"""
    voiceprints = list(_voiceprints.values())
    return APIResponse(
        success=True,
        message=f"Found {len(voiceprints)} voiceprints",
        data=voiceprints
    )


@router.post("/enroll", response_model=APIResponse, status_code=status.HTTP_201_CREATED)
async def enroll_voiceprint(voiceprint_data: VoiceprintCreate):
    """录制新声纹"""
    # 生成唯一 ID
    voiceprint_id = str(uuid.uuid4())
    
    # 模拟声纹处理和质量评分
    quality_score = min(voiceprint_data.quality_threshold + 0.1, 1.0)
    
    # 创建声纹对象
    voiceprint = Voiceprint(
        id=voiceprint_id,
        user_id=voiceprint_data.user_id,
        embedding_data="encrypted_embedding_" + voiceprint_id,  # 模拟加密数据
        quality_score=quality_score,
        created_at=datetime.now(),
        sample_count=len(voiceprint_data.audio_samples)
    )
    
    # 存储到内存
    _voiceprints[voiceprint_id] = voiceprint
    
    return APIResponse(
        success=True,
        message="Voiceprint enrolled successfully",
        data=voiceprint.model_dump()
    )


@router.get("/{voiceprint_id}", response_model=APIResponse)
async def get_voiceprint(voiceprint_id: str):
    """获取指定声纹信息"""
    if voiceprint_id not in _voiceprints:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Voiceprint not found"
        )
    
    voiceprint = _voiceprints[voiceprint_id]
    return APIResponse(
        success=True,
        message="Voiceprint found",
        data=voiceprint.model_dump()
    )


@router.post("/verify", response_model=APIResponse)
async def verify_voiceprint(verification_data: dict):
    """验证声纹"""
    # 模拟声纹验证逻辑
    audio_sample = verification_data.get("audio_sample")
    user_id = verification_data.get("user_id")
    
    if not audio_sample or not user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Missing audio_sample or user_id"
        )
    
    # 查找用户的声纹
    user_voiceprints = [vp for vp in _voiceprints.values() if vp.user_id == user_id]
    
    if not user_voiceprints:
        return APIResponse(
            success=True,
            message="Verification completed",
            data={
                "verified": False,
                "confidence": 0.0,
                "reason": "No voiceprint found for user"
            }
        )
    
    # 模拟验证结果
    confidence = 0.85  # 模拟置信度
    verified = confidence > 0.7
    
    return APIResponse(
        success=True,
        message="Verification completed",
        data={
            "verified": verified,
            "confidence": confidence,
            "user_id": user_id
        }
    )


@router.delete("/{voiceprint_id}", response_model=APIResponse)
async def delete_voiceprint(voiceprint_id: str):
    """删除声纹"""
    if voiceprint_id not in _voiceprints:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Voiceprint not found"
        )
    
    del _voiceprints[voiceprint_id]
    
    return APIResponse(
        success=True,
        message="Voiceprint deleted successfully",
        data={"voiceprint_id": voiceprint_id}
    )
