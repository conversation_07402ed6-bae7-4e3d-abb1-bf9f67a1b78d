"""
会话历史管理 API 路由
"""

from fastapi import APIRouter, HTTPException, status
from typing import List, Optional
import uuid
from datetime import datetime

from ...shared.models import ConversationSession, Utterance, APIResponse

router = APIRouter()

# 临时内存存储
_conversations: dict[str, ConversationSession] = {}
_utterances: dict[str, Utterance] = {}


@router.get("/", response_model=APIResponse)
async def list_conversations(
    limit: int = 50,
    offset: int = 0,
    status_filter: Optional[str] = None
):
    """列出会话历史"""
    conversations = list(_conversations.values())
    
    # 状态过滤
    if status_filter:
        conversations = [c for c in conversations if c.status == status_filter]
    
    # 分页
    total = len(conversations)
    conversations = conversations[offset:offset + limit]
    
    return APIResponse(
        success=True,
        message=f"Found {total} conversations",
        data={
            "conversations": conversations,
            "total": total,
            "limit": limit,
            "offset": offset
        }
    )


@router.get("/{conversation_id}", response_model=APIResponse)
async def get_conversation(conversation_id: str):
    """获取指定会话详情"""
    if conversation_id not in _conversations:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )
    
    conversation = _conversations[conversation_id]
    
    # 获取会话中的所有语句
    utterances = [
        _utterances[utt_id] for utt_id in conversation.utterances 
        if utt_id in _utterances
    ]
    
    return APIResponse(
        success=True,
        message="Conversation found",
        data={
            "conversation": conversation.model_dump(),
            "utterances": [utt.model_dump() for utt in utterances]
        }
    )


@router.get("/{conversation_id}/export", response_model=APIResponse)
async def export_conversation(conversation_id: str, format: str = "json"):
    """导出会话数据"""
    if conversation_id not in _conversations:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )
    
    conversation = _conversations[conversation_id]
    utterances = [
        _utterances[utt_id] for utt_id in conversation.utterances 
        if utt_id in _utterances
    ]
    
    if format == "json":
        export_data = {
            "conversation": conversation.model_dump(),
            "utterances": [utt.model_dump() for utt in utterances],
            "exported_at": datetime.now().isoformat()
        }
    elif format == "text":
        # 简单文本格式
        lines = [f"会话 {conversation.id} - {conversation.summary or '无摘要'}"]
        lines.append(f"开始时间: {datetime.fromtimestamp(conversation.started_at or 0)}")
        lines.append("=" * 50)
        
        for utt in utterances:
            speaker = utt.speaker_user_id or "未知说话人"
            lines.append(f"[{speaker}]: {utt.text}")
        
        export_data = "\n".join(lines)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Unsupported export format"
        )
    
    return APIResponse(
        success=True,
        message="Conversation exported successfully",
        data={
            "format": format,
            "content": export_data
        }
    )


@router.delete("/{conversation_id}", response_model=APIResponse)
async def delete_conversation(conversation_id: str):
    """删除会话"""
    if conversation_id not in _conversations:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )
    
    conversation = _conversations[conversation_id]
    
    # 删除相关的语句
    for utt_id in conversation.utterances:
        if utt_id in _utterances:
            del _utterances[utt_id]
    
    # 删除会话
    del _conversations[conversation_id]
    
    return APIResponse(
        success=True,
        message="Conversation deleted successfully",
        data={"conversation_id": conversation_id}
    )


# 辅助函数：创建示例数据（用于测试）
def _create_sample_data():
    """创建一些示例会话数据"""
    if _conversations:
        return  # 已有数据，不重复创建
    
    # 创建示例会话
    session_id = str(uuid.uuid4())
    session = ConversationSession(
        id=session_id,
        started_at=datetime.now().timestamp() - 3600,  # 1小时前
        last_updated_at=datetime.now().timestamp(),
        participants=["user_1", "user_2"],
        summary="讨论今天的天气和出行计划",
        status="closed"
    )
    
    # 创建示例语句
    utterances = [
        Utterance(
            id=str(uuid.uuid4()),
            session_id=session_id,
            text="今天天气怎么样？",
            start_ts=session.started_at,
            end_ts=session.started_at + 2,
            speaker_user_id="user_1",
            confidence=0.95
        ),
        Utterance(
            id=str(uuid.uuid4()),
            session_id=session_id,
            text="今天是晴天，适合出门。",
            start_ts=session.started_at + 5,
            end_ts=session.started_at + 8,
            speaker_user_id="user_2",
            confidence=0.92
        )
    ]
    
    # 存储数据
    _conversations[session_id] = session
    for utt in utterances:
        _utterances[utt.id] = utt
        session.utterances.append(utt.id)


# 在模块加载时创建示例数据
_create_sample_data()
