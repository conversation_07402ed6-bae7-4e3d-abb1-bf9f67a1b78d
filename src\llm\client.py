"""
LLM 客户端实现
支持 OpenAI API 和结构化输出
"""

import json
import time
import uuid
from typing import Optional, Dict, Any, List
from openai import OpenAI

from ..shared.models import LLMConfig, LLMAnalysisResult
from .schemas import (
    ConversationAnalysisRequest, 
    ConversationAnalysisResponse,
    get_analysis_result_schema,
    LLMMetrics
)


class LLMClient:
    """LLM 客户端类"""
    
    def __init__(self, config: LLMConfig):
        """初始化 LLM 客户端"""
        self.config = config
        self.metrics = LLMMetrics()
        
        # 初始化 OpenAI 客户端
        client_kwargs = {
            "api_key": config.api_key,
            "timeout": config.timeout
        }
        
        if config.base_url:
            client_kwargs["base_url"] = config.base_url
            
        self.client = OpenAI(**client_kwargs)
    
    def analyze_conversation(self, request: ConversationAnalysisRequest) -> ConversationAnalysisResponse:
        """分析对话并返回结构化结果"""
        start_time = time.time()
        request_id = str(uuid.uuid4())
        
        try:
            # 构建提示
            system_prompt = self._build_system_prompt()
            user_prompt = self._build_user_prompt(request)
            
            # 获取 JSON Schema
            analysis_schema = get_analysis_result_schema()
            
            # 调用 OpenAI API
            response = self.client.chat.completions.create(
                model=self.config.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                response_format={
                    "type": "json_schema",
                    "json_schema": {
                        "name": "ConversationAnalysis",
                        "schema": analysis_schema
                    }
                },
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens
            )
            
            # 解析响应
            content = response.choices[0].message.content
            analysis_data = json.loads(content)
            analysis_result = LLMAnalysisResult(**analysis_data)
            
            # 计算处理时间
            processing_time_ms = int((time.time() - start_time) * 1000)
            
            # 更新指标
            self._update_metrics(success=True, response_time_ms=processing_time_ms)
            
            return ConversationAnalysisResponse(
                request_id=request_id,
                analysis=analysis_result,
                processing_time_ms=processing_time_ms,
                model_used=self.config.model,
                tokens_used=response.usage.total_tokens if response.usage else None
            )
            
        except Exception as e:
            # 更新失败指标
            processing_time_ms = int((time.time() - start_time) * 1000)
            self._update_metrics(success=False, response_time_ms=processing_time_ms)
            raise e
    
    def _build_system_prompt(self) -> str:
        """构建系统提示"""
        return """你是一个智能家庭助手，负责分析家庭成员之间的对话，判断是否需要介入并提供帮助。

你的职责：
1. 分析对话内容，判断是否需要助手介入
2. 如果需要介入，确定需要调用的工具或控制的设备
3. 生成合适的回复文本
4. 评估请求的优先级和安全性

介入条件：
- 用户明确提出问题或请求
- 讨论的内容可以通过工具查询获得更多信息
- 需要控制智能家居设备
- 用户表达困扰或需要帮助
- 情况可能需要紧急处理

可用工具类型：
- web_search: 网络搜索
- weather_search: 天气查询
- calendar_check: 日历查询
- navigation: 导航路径
- calculation: 数学计算

可控制设备类型：
- light: 灯光控制
- climate: 空调/暖气
- media_player: 媒体播放器
- scene: 场景控制

安全准则：
- 不执行可能危险的操作
- 保护用户隐私
- 不提供医疗建议
- 不执行金融交易

你必须返回严格符合 JSON Schema 的响应，不得包含任何其他文本。"""
    
    def _build_user_prompt(self, request: ConversationAnalysisRequest) -> str:
        """构建用户提示"""
        prompt_parts = [
            f"对话内容：{request.conversation_text}",
            f"参与者：{', '.join(request.participants)}"
        ]
        
        if request.context:
            prompt_parts.append(f"上下文：{request.context}")
        
        if request.available_tools:
            prompt_parts.append(f"可用工具：{', '.join(request.available_tools)}")
        
        if request.available_devices:
            device_list = []
            for device in request.available_devices:
                device_info = f"{device.get('name', 'Unknown')} ({device.get('entity_id', 'unknown')})"
                device_list.append(device_info)
            prompt_parts.append(f"可用设备：{', '.join(device_list)}")
        
        if request.user_preferences:
            prompt_parts.append(f"用户偏好：{json.dumps(request.user_preferences, ensure_ascii=False)}")
        
        prompt_parts.append("\n请分析以上对话并返回 JSON 格式的分析结果。")
        
        return "\n".join(prompt_parts)
    
    def _update_metrics(self, success: bool, response_time_ms: int):
        """更新调用指标"""
        self.metrics.total_requests += 1
        self.metrics.last_request_time = time.time()
        
        if success:
            self.metrics.successful_requests += 1
        else:
            self.metrics.failed_requests += 1
        
        # 更新平均响应时间
        total_time = (self.metrics.average_response_time_ms * (self.metrics.total_requests - 1) + 
                     response_time_ms)
        self.metrics.average_response_time_ms = total_time / self.metrics.total_requests
        
        # 更新错误率
        self.metrics.error_rate = self.metrics.failed_requests / self.metrics.total_requests
    
    def get_metrics(self) -> LLMMetrics:
        """获取调用指标"""
        return self.metrics
    
    def test_connection(self) -> Dict[str, Any]:
        """测试 LLM 连接"""
        try:
            # 发送简单的测试请求
            response = self.client.chat.completions.create(
                model=self.config.model,
                messages=[
                    {"role": "user", "content": "Hello, this is a connection test."}
                ],
                max_tokens=10,
                temperature=0
            )
            
            return {
                "success": True,
                "model": self.config.model,
                "response": response.choices[0].message.content,
                "usage": response.usage.model_dump() if response.usage else None
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "model": self.config.model
            }
    
    def update_config(self, new_config: LLMConfig):
        """更新配置"""
        self.config = new_config
        
        # 重新初始化客户端
        client_kwargs = {
            "api_key": new_config.api_key,
            "timeout": new_config.timeout
        }
        
        if new_config.base_url:
            client_kwargs["base_url"] = new_config.base_url
            
        self.client = OpenAI(**client_kwargs)
