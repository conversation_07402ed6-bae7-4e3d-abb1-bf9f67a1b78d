"""
测试核心 Pydantic 数据模型
遵循 TDD 原则，先定义期望的模型行为
"""

import pytest
from datetime import datetime
from typing import Dict, Any
from pydantic import ValidationError

from src.shared.models import (
    FamilyMember,
    FamilyMemberCreate,
    Voiceprint,
    VoiceprintCreate,
    LLMConfig,
    Device,
    DeviceType,
    SystemStatus,
    Utterance,
    ConversationSession,
    LLMAnalysisResult,
    ToolCall,
    HomeAction
)


class TestFamilyMember:
    """测试家庭成员模型"""
    
    def test_create_family_member_minimal(self):
        """测试创建最小化家庭成员"""
        member_data = {
            "display_name": "张三"
        }
        member = FamilyMemberCreate(**member_data)
        assert member.display_name == "张三"
        assert member.relationship is None
        assert member.device_permissions == []
        assert member.preferences == {}
    
    def test_create_family_member_full(self):
        """测试创建完整家庭成员信息"""
        member_data = {
            "display_name": "李四",
            "relationship": "父亲",
            "contact_info": {"phone": "13800000000", "email": "<EMAIL>"},
            "device_permissions": ["living_room.light", "bedroom.climate"],
            "preferences": {"language": "zh-CN", "voice_speed": 1.2}
        }
        member = FamilyMemberCreate(**member_data)
        assert member.display_name == "李四"
        assert member.relationship == "父亲"
        assert len(member.device_permissions) == 2
        assert member.preferences["language"] == "zh-CN"
    
    def test_family_member_name_validation(self):
        """测试姓名验证"""
        # 空名称应该失败
        with pytest.raises(ValidationError):
            FamilyMemberCreate(display_name="")
        
        # 过长名称应该失败
        with pytest.raises(ValidationError):
            FamilyMemberCreate(display_name="a" * 51)
    
    def test_family_member_with_timestamps(self):
        """测试带时间戳的完整家庭成员模型"""
        now = datetime.now()
        member_data = {
            "user_id": "user_123",
            "display_name": "王五",
            "relationship": "母亲",
            "created_at": now,
            "updated_at": now,
            "voiceprint_id": "vp_456",
            "is_active": True
        }
        member = FamilyMember(**member_data)
        assert member.user_id == "user_123"
        assert member.voiceprint_id == "vp_456"
        assert member.is_active is True


class TestVoiceprint:
    """测试声纹模型"""
    
    def test_create_voiceprint_request(self):
        """测试声纹创建请求"""
        vp_data = {
            "user_id": "user_123",
            "audio_samples": ["base64_audio_1", "base64_audio_2"],
            "quality_threshold": 0.85
        }
        vp_create = VoiceprintCreate(**vp_data)
        assert vp_create.user_id == "user_123"
        assert len(vp_create.audio_samples) == 2
        assert vp_create.quality_threshold == 0.85
    
    def test_voiceprint_model(self):
        """测试声纹存储模型"""
        now = datetime.now()
        vp_data = {
            "id": "vp_123",
            "user_id": "user_456",
            "embedding_data": "encrypted_embedding_data",
            "quality_score": 0.92,
            "created_at": now,
            "sample_count": 3
        }
        vp = Voiceprint(**vp_data)
        assert vp.id == "vp_123"
        assert vp.quality_score == 0.92
        assert vp.sample_count == 3


class TestLLMConfig:
    """测试 LLM 配置模型"""
    
    def test_default_llm_config(self):
        """测试默认 LLM 配置"""
        config = LLMConfig(api_key="sk-test123")
        assert config.provider == "openai"
        assert config.model == "gpt-4o-mini"
        assert config.temperature == 0.2
        assert config.timeout == 30
    
    def test_custom_llm_config(self):
        """测试自定义 LLM 配置"""
        config_data = {
            "provider": "anthropic",
            "api_key": "sk-ant-test",
            "model": "claude-3-sonnet",
            "base_url": "https://api.anthropic.com",
            "temperature": 0.1,
            "max_tokens": 4000,
            "timeout": 60
        }
        config = LLMConfig(**config_data)
        assert config.provider == "anthropic"
        assert config.model == "claude-3-sonnet"
        assert config.max_tokens == 4000


class TestDevice:
    """测试设备模型"""
    
    def test_device_creation(self):
        """测试设备创建"""
        now = datetime.now()
        device_data = {
            "id": "dev_123",
            "name": "客厅灯",
            "device_type": DeviceType.LIGHT,
            "entity_id": "light.living_room",
            "capabilities": ["turn_on", "turn_off", "set_brightness"],
            "current_state": {"state": "on", "brightness": 80},
            "is_online": True,
            "last_seen": now
        }
        device = Device(**device_data)
        assert device.name == "客厅灯"
        assert device.device_type == DeviceType.LIGHT
        assert len(device.capabilities) == 3
        assert device.current_state["brightness"] == 80


class TestConversation:
    """测试会话相关模型"""
    
    def test_utterance_creation(self):
        """测试语句创建"""
        utt_data = {
            "id": "utt_123",
            "session_id": "sess_456",
            "text": "今天天气怎么样？",
            "start_ts": 1234567890.5,
            "end_ts": 1234567892.0,
            "speaker_user_id": "user_789",
            "confidence": 0.95
        }
        utterance = Utterance(**utt_data)
        assert utterance.text == "今天天气怎么样？"
        assert utterance.confidence == 0.95
        assert utterance.end_ts - utterance.start_ts == 1.5
    
    def test_conversation_session(self):
        """测试会话会话"""
        session_data = {
            "id": "sess_123",
            "started_at": 1234567890.0,
            "last_updated_at": 1234567900.0,
            "participants": ["user_456", "user_789"],
            "utterances": ["utt_1", "utt_2", "utt_3"],
            "summary": "讨论今天的天气和出行计划",
            "status": "active"
        }
        session = ConversationSession(**session_data)
        assert len(session.participants) == 2
        assert len(session.utterances) == 3
        assert session.status == "active"


class TestLLMAnalysis:
    """测试 LLM 分析结果模型"""
    
    def test_minimal_analysis_result(self):
        """测试最小化分析结果"""
        result_data = {
            "should_intervene": False,
            "reasons": "对话内容为日常闲聊，无需助手介入"
        }
        result = LLMAnalysisResult(**result_data)
        assert result.should_intervene is False
        assert result.suggested_tools == []
        assert result.home_actions == []
        assert result.priority == "normal"
    
    def test_full_analysis_result(self):
        """测试完整分析结果"""
        result_data = {
            "should_intervene": True,
            "reasons": "用户询问天气信息，需要调用天气查询工具",
            "suggested_tools": [
                {"name": "weather_search", "args": {"location": "北京"}},
                {"name": "calendar_check", "args": {"date": "today"}}
            ],
            "home_actions": [
                {
                    "domain": "light",
                    "entity_id": "light.living_room",
                    "action": "turn_on",
                    "params": {"brightness": 70}
                }
            ],
            "reply_text": "我来为您查询北京的天气情况",
            "priority": "normal",
            "safety_flags": []
        }
        result = LLMAnalysisResult(**result_data)
        assert result.should_intervene is True
        assert len(result.suggested_tools) == 2
        assert len(result.home_actions) == 1
        assert result.suggested_tools[0].name == "weather_search"
        assert result.home_actions[0].domain == "light"


class TestSystemStatus:
    """测试系统状态模型"""
    
    def test_system_status(self):
        """测试系统状态"""
        now = datetime.now()
        status_data = {
            "standby_active": True,
            "audio_capture_active": True,
            "llm_connected": True,
            "devices_count": 15,
            "active_sessions": 2,
            "uptime_seconds": 3600,
            "last_activity": now
        }
        status = SystemStatus(**status_data)
        assert status.standby_active is True
        assert status.devices_count == 15
        assert status.uptime_seconds == 3600
