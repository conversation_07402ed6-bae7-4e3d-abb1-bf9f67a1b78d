# 项目目录结构定义
# 这个文件用于定义项目的模块结构和依赖关系

# 模块列表
MODULES = [
    'voice_recognition',  # 语音识别模块
    'memory_module',       # 记忆模块
    'ai_processing',       # AI处理模块
    'speech_synthesis',    # 语音合成模块
    'main'                 # 主程序模块
]

# 依赖关系
DEPENDENCIES = {
    'voice_recognition': ['faster-whisper', 'whisperX'],
    'memory_module': ['mem0'],
    'ai_processing': [],
    'speech_synthesis': ['pyttsx3', 'gTTS'],
    'main': []
}
