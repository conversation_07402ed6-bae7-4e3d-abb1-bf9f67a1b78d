"""
LLM 相关的 Pydantic 模式定义
用于结构化输入和输出
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..shared.models import LLMAnalysisResult


class ConversationAnalysisRequest(BaseModel):
    """对话分析请求模型"""
    conversation_text: str = Field(..., description="对话文本内容")
    participants: List[str] = Field(..., description="参与者列表")
    context: Optional[str] = Field(None, description="对话上下文")
    session_id: Optional[str] = Field(None, description="会话ID")
    timestamp: Optional[datetime] = Field(None, description="时间戳")
    available_tools: Optional[List[str]] = Field(None, description="可用工具列表")
    available_devices: Optional[List[Dict[str, Any]]] = Field(None, description="可用设备列表")
    user_preferences: Optional[Dict[str, Any]] = Field(None, description="用户偏好设置")


class ConversationAnalysisResponse(BaseModel):
    """对话分析响应模型"""
    request_id: Optional[str] = Field(None, description="请求ID")
    analysis: LLMAnalysisResult = Field(..., description="分析结果")
    processing_time_ms: Optional[int] = Field(None, description="处理时间(毫秒)")
    model_used: Optional[str] = Field(None, description="使用的模型")
    tokens_used: Optional[int] = Field(None, description="使用的token数量")


class ToolDescription(BaseModel):
    """工具描述模型"""
    name: str = Field(..., description="工具名称")
    description: str = Field(..., description="工具描述")
    parameters: Dict[str, Any] = Field(..., description="参数schema")
    examples: Optional[List[str]] = Field(None, description="使用示例")


class DeviceDescription(BaseModel):
    """设备描述模型"""
    entity_id: str = Field(..., description="设备实体ID")
    name: str = Field(..., description="设备名称")
    device_type: str = Field(..., description="设备类型")
    capabilities: List[str] = Field(..., description="设备能力")
    current_state: Dict[str, Any] = Field(..., description="当前状态")


class SystemPromptConfig(BaseModel):
    """系统提示配置模型"""
    role_description: str = Field(..., description="角色描述")
    capabilities: List[str] = Field(..., description="能力列表")
    constraints: List[str] = Field(..., description="约束条件")
    output_format: str = Field(..., description="输出格式要求")
    safety_guidelines: List[str] = Field(..., description="安全准则")


class LLMMetrics(BaseModel):
    """LLM 调用指标模型"""
    total_requests: int = Field(0, description="总请求数")
    successful_requests: int = Field(0, description="成功请求数")
    failed_requests: int = Field(0, description="失败请求数")
    average_response_time_ms: float = Field(0.0, description="平均响应时间")
    total_tokens_used: int = Field(0, description="总token使用量")
    last_request_time: Optional[datetime] = Field(None, description="最后请求时间")
    error_rate: float = Field(0.0, description="错误率")


# JSON Schema 生成辅助函数
def get_analysis_result_schema() -> Dict[str, Any]:
    """获取 LLMAnalysisResult 的 JSON Schema"""
    return LLMAnalysisResult.model_json_schema()


def get_tool_call_schema() -> Dict[str, Any]:
    """获取工具调用的 JSON Schema"""
    from ..shared.models import ToolCall
    return ToolCall.model_json_schema()


def get_home_action_schema() -> Dict[str, Any]:
    """获取家居动作的 JSON Schema"""
    from ..shared.models import HomeAction
    return HomeAction.model_json_schema()
